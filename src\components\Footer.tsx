import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useToast } from '@/hooks/use-toast';
import { Mail } from 'lucide-react';
import Logo from './Logo';
import { AnimatedSection } from '@/components/animations';
import { motion } from 'framer-motion';
import { staggerContainer, staggerItem, getReducedMotionVariants } from '@/lib/animations';
const Footer = () => {
  const [email, setEmail] = useState('');
  const {
    toast
  } = useToast();
  const handleNewsletterSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!email) {
      toast({
        title: "Email Required",
        description: "Please enter your email address.",
        variant: "destructive"
      });
      return;
    }

    // Simulate newsletter subscription
    toast({
      title: "Subscribed!",
      description: "Thank you for subscribing to our newsletter."
    });
    setEmail('');
  };
  return <AnimatedSection className="w-full py-8 px-6 md:px-12 border-t border-border bg-background">
      <motion.div
        className="max-w-4xl mx-auto"
        variants={getReducedMotionVariants(staggerContainer)}
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true, margin: "-50px" }}
      >
        {/* Main Footer Content */}
        <motion.div
          className="flex flex-col md:flex-row items-center justify-between gap-8"
          variants={getReducedMotionVariants(staggerContainer)}
        >
          {/* Logo and Navigation */}
          <motion.div
            className="flex flex-col md:flex-row items-center gap-6 md:gap-8"
            variants={getReducedMotionVariants(staggerItem)}
          >
            <Logo />
            <nav className="flex flex-wrap items-center gap-6 text-sm">
              <Link to="/" className="text-muted-foreground hover:text-primary transition-colors">
                Artists
              </Link>
              <Link to="/agency" className="text-muted-foreground hover:text-primary transition-colors">
                Agency
              </Link>
              <Link to="/about" className="text-muted-foreground hover:text-primary transition-colors">
                About
              </Link>
              <Link to="/contact" className="text-muted-foreground hover:text-primary transition-colors">
                Contact
              </Link>
            </nav>
          </motion.div>

          {/* Newsletter */}
          <motion.div
            className="flex flex-col items-center md:items-end gap-3"
            variants={getReducedMotionVariants(staggerItem)}
          >
            <form onSubmit={handleNewsletterSubmit} className="flex gap-2">
              <Input
                type="email"
                placeholder="Your email"
                value={email}
                onChange={e => setEmail(e.target.value)}
                className="h-9 w-48 text-sm"
              />
              <Button type="submit" size="sm" className="h-9 px-3">
                Subscribe
                <Mail className="h-3 w-3" />
              </Button>
            </form>
          </motion.div>
        </motion.div>

        {/* Copyright */}
        <motion.div
          className="mt-6 pt-6 border-t border-border text-center text-muted-foreground text-xs"
          variants={getReducedMotionVariants(staggerItem)}
        >
          © 2025 Crash Events. Exceptional artist management.
        </motion.div>
      </motion.div>
    </AnimatedSection>;
};
export default Footer;