import React, { useState, useEffect } from 'react';
import { <PERSON> } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { heroContainer, heroTitle, heroSubtitle, staggerContainer, staggerItem, fadeInUp, getReducedMotionVariants } from '@/lib/animations';
import { AnimatedSection } from '@/components/animations';

interface Artist {
  id: string;
  name: string;
  image: string;
  genres: string[];
}

const artists: Artist[] = [
  {
    id: '1',
    name: 'DJ <PERSON>',
    image: '/artists/jaywallbe.jpg',
    genres: ['Drum & Bass', 'Allround']
  },
  {
    id: '2',
    name: 'DJ <PERSON>',
    image: '/artists/dj_arrow_official.jpg',
    genres: ['Urban', 'R&B', 'Allround']
  },
  {
    id: '3',
    name: 'DJ Snezzy',
    image: '/artists/snezzy_official_.jpg',
    genres: ['Techno', 'House', 'Allround']
  },
  {
    id: '4',
    name: 'DJ Artic',
    image: '/artists/deejay_artic.jpg',
    genres: ['Allround', 'Commercial']
  },
  {
    id: '5',
    name: 'DJ B<PERSON>Code',
    image: '/artists/dj.bcode.jpg',
    genres: ['Allround', 'Commercial']
  },
  {
    id: '6',
    name: 'DJ Bonuzz',
    image: '/artists/djbonuzz.jpg',
    genres: ['Allround', 'Commercial']
  }
];

const genres = ['All', 'Electronic', 'House', 'Techno', 'Progressive'];

const HeroSection = () => {
  const [selectedGenre, setSelectedGenre] = useState('All');
  const [displayedArtists, setDisplayedArtists] = useState(artists);
  const [isTransitioning, setIsTransitioning] = useState(false);

  const filteredArtists = selectedGenre === 'All' 
    ? artists 
    : artists.filter(artist => artist.genres.includes(selectedGenre));

  const handleGenreClick = (genre: string) => {
    if (genre !== selectedGenre) {
      setIsTransitioning(true);
      
      // Phase 1: Fade out current artists
      setTimeout(() => {
        setSelectedGenre(genre);
        const newFilteredArtists = genre === 'All' 
          ? artists 
          : artists.filter(artist => artist.genres.includes(genre));
        setDisplayedArtists(newFilteredArtists);
      }, 250);
      
      // Phase 2: Fade in new artists
      setTimeout(() => {
        setIsTransitioning(false);
      }, 300);
    }
  };

  // Update displayed artists when not transitioning
  useEffect(() => {
    if (!isTransitioning) {
      setDisplayedArtists(filteredArtists);
    }
  }, [filteredArtists, isTransitioning]);

  return (
    <section className="relative w-full py-12 md:py-20 px-6 md:px-12 flex flex-col items-center justify-center overflow-hidden">
      <motion.div
        className="relative z-10 max-w-4xl text-center space-y-6"
        variants={getReducedMotionVariants(heroContainer)}
        initial="hidden"
        animate="visible"
      >
        <motion.div
          className="flex justify-center"
          variants={getReducedMotionVariants(fadeInUp)}
        >
          <Link to="/contact">
            <span className="border border-border hover:bg-accent inline-flex items-center gap-2 px-3 py-1 text-xs font-medium rounded-full text-primary hover:text-foreground transition-colors cursor-pointer">
              Are you an artist? Let's talk!
            </span>
          </Link>
        </motion.div>

        <motion.h1
          className="text-4xl md:text-6xl lg:text-7xl font-semibold text-balance text-foreground"
          variants={getReducedMotionVariants(heroTitle)}
        >
          Exceptional <span className="text-primary">artist</span> management.
        </motion.h1>

        <motion.div
          className="flex justify-center pt-2"
          variants={getReducedMotionVariants(heroSubtitle)}
        >
          <Link to="/agency">
            <button className="group inline-flex items-center gap-2 text-lg md:text-xl text-foreground hover:text-foreground transition-all duration-200 cursor-pointer">
              <span>Discover what we do</span>
              <svg
                className="w-5 h-5 transition-transform duration-200 group-hover:translate-x-1"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </button>
          </Link>
        </motion.div>

        {/* Genre Filter System */}
        <motion.div
          className="pt-16 flex flex-wrap justify-center gap-2"
          variants={getReducedMotionVariants(staggerContainer)}
        >
          {genres.map((genre) => (
            <motion.span
              key={genre}
              onClick={() => handleGenreClick(genre)}
              className={`px-3 py-1 text-xs font-medium rounded-full cursor-pointer transition-all duration-300 transform hover:scale-102 ${
                selectedGenre === genre
                  ? 'bg-primary text-primary-foreground hover:bg-primary/80 scale-102'
                  : 'border border-border text-foreground hover:bg-accent hover:text-accent-foreground'
              }`}
              variants={getReducedMotionVariants(staggerItem)}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              {genre}
            </motion.span>
          ))}
        </motion.div>
      </motion.div>

      {/* Artist Grid */}
      <AnimatedSection
        className="w-full max-w-5xl mx-auto mt-12 z-10"
        delay={0.5}
        variants={getReducedMotionVariants(fadeInUp)}
      >
        <AnimatePresence mode="wait">
          <motion.div
            key={selectedGenre}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 justify-center"
            variants={getReducedMotionVariants(staggerContainer)}
            initial="hidden"
            animate="visible"
            exit="hidden"
          >
            {displayedArtists.map((artist) => (
              <motion.div
                key={artist.id}
                variants={getReducedMotionVariants(staggerItem)}
                whileHover={{ scale: 1.005, y: -2 }}
                whileTap={{ scale: 0.995 }}
              >
                <Link
                  to={`/artist/${artist.id}`}
                  className="group relative overflow-hidden rounded-xl bg-card border border-border hover:border-primary/30 transition-all duration-300 cursor-pointer block"
                >
                  <div className="aspect-[3/4] overflow-hidden">
                    <img
                      src={artist.image}
                      alt={artist.name}
                      className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
                    />
                  </div>
                  <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                    <div className="absolute bottom-4 left-4 right-4 text-white">
                      <h3 className="font-semibold text-lg mb-2">{artist.name}</h3>
                      <div className="flex gap-1 flex-wrap">
                        {artist.genres.map((genre) => (
                          <span key={genre} className="px-2 py-1 text-xs font-medium rounded-full border border-white/50 text-white hover:bg-white/20 transition-colors">
                            {genre}
                          </span>
                        ))}
                      </div>
                    </div>
                  </div>
                </Link>
              </motion.div>
            ))}
          </motion.div>
        </AnimatePresence>
      </AnimatedSection>
    </section>
  );
};

export default HeroSection;
